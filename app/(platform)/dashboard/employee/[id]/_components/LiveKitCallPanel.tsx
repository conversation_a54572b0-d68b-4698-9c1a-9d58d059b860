"use client";

import "@livekit/components-styles";
import {
  <PERSON><PERSON>udioR<PERSON><PERSON>,
  useRoomContext,
  useTrackToggle,
  useAudioWaveform,
  useTracks,
  useLocalParticipant,
  useRemoteParticipants,
} from "@livekit/components-react";
import { Track } from "livekit-client";
import { X, Mic, MicOff } from "lucide-react";
import { useState, useEffect } from "react";

interface LiveKitCallPanelProps {
  handleHidePanel: () => void;
}

/**
 * Custom Microphone Toggle Button Component
 */
const MicToggleButton = () => {
  try {
    const { toggle, enabled, pending } = useTrackToggle({
      source: Track.Source.Microphone,
    });

    return (
      <button
        onClick={() => toggle()}
        disabled={pending}
        className="p-4 rounded-full transition-all duration-200 text-white shadow-lg hover:scale-105 flex items-center justify-center"
        style={{
          backgroundColor: enabled ? "#ae00d0" : "#e01e5a",
          width: "56px",
          height: "56px",
        }}
        aria-label={enabled ? "Mute microphone" : "Unmute microphone"}
      >
        {enabled ? <Mic className="h-6 w-6" /> : <MicOff className="h-6 w-6" />}
      </button>
    );
  } catch (error) {
    console.error("MicToggleButton error:", error);
    return (
      <button
        className="p-4 rounded-full bg-gray-500 text-white shadow-lg flex items-center justify-center"
        style={{ width: "56px", height: "56px" }}
      >
        <Mic className="h-6 w-6" />
      </button>
    );
  }
};

/**
 * Custom Disconnect Button Component
 */
const DisconnectButton = ({ onDisconnect }: { onDisconnect: () => void }) => {
  try {
    const { toggle, enabled } = useTrackToggle({
      source: Track.Source.Microphone,
    });

    const handleDisconnect = async () => {
      try {
        // Mute the microphone if it's currently enabled
        if (enabled) {
          await toggle();
        }
        // Close the panel
        onDisconnect();
      } catch (error) {
        console.error("Error disconnecting:", error);
        // Still close the panel even if muting fails
        onDisconnect();
      }
    };

    return (
      <button
        onClick={handleDisconnect}
        className="p-4 rounded-full transition-all duration-200 text-white shadow-lg hover:scale-105 flex items-center justify-center"
        style={{
          backgroundColor: "#e01e5a",
          width: "56px",
          height: "56px",
        }}
        aria-label="Disconnect and close panel"
      >
        <X className="h-6 w-6" />
      </button>
    );
  } catch (error) {
    console.error("DisconnectButton error:", error);
    return (
      <button
        onClick={onDisconnect}
        className="p-4 rounded-full bg-red-500 text-white shadow-lg flex items-center justify-center"
        style={{ width: "56px", height: "56px" }}
      >
        <X className="h-6 w-6" />
      </button>
    );
  }
};

/**
 * Custom Audio Waveform Visualizer Component
 */
const AudioWaveformVisualizer = () => {
  const [animationBars, setAnimationBars] = useState<number[]>([]);

  try {
    const localParticipant = useLocalParticipant();
    const remoteParticipants = useRemoteParticipants();

    // Get all audio tracks (both local and remote)
    const tracks = useTracks([Track.Source.Microphone], {
      onlySubscribed: false,
    });

    // Get the local microphone track
    const localMicTrack =
      localParticipant.microphoneTrack?.track ||
      tracks.find(
        (t) => t.source === Track.Source.Microphone && t.participant.isLocal
      )?.track;

    // Get the remote participant's (agent's) audio track
    const remoteMicTrack =
      remoteParticipants.length > 0
        ? remoteParticipants[0].audioTrackPublications.values().next().value
            ?.track
        : null;

    // Use local audio waveform data
    const { bars: localBars } = useAudioWaveform(localMicTrack);

    // Use remote audio waveform data
    const { bars: remoteBars } = useAudioWaveform(remoteMicTrack);

    // Combine local and remote audio data for visualization
    useEffect(() => {
      // Combine both local and remote audio data
      let combinedBars: number[] = [];

      // If we have both local and remote bars, combine them by taking the maximum value at each position
      if (
        localBars &&
        localBars.length > 0 &&
        remoteBars &&
        remoteBars.length > 0
      ) {
        const maxLength = Math.max(localBars.length, remoteBars.length);
        combinedBars = Array.from({ length: maxLength }, (_, i) => {
          const localValue = localBars[i] || 0;
          const remoteValue = remoteBars[i] || 0;
          return Math.max(localValue, remoteValue);
        });
      }
      // If only local bars are available
      else if (localBars && localBars.length > 0) {
        combinedBars = localBars;
      }
      // If only remote bars are available
      else if (remoteBars && remoteBars.length > 0) {
        combinedBars = remoteBars;
      }

      if (combinedBars.length > 0) {
        setAnimationBars(combinedBars);
      } else {
        // Create animated bars when no real audio data
        const interval = setInterval(() => {
          const newBars = Array.from(
            { length: 12 },
            () => Math.random() * 0.8 + 0.2
          );
          setAnimationBars(newBars);
        }, 150);

        return () => clearInterval(interval);
      }
    }, [localBars, remoteBars]);

    // Use the animation bars which now contain either real audio data or fallback animation
    const displayBars = animationBars;

    // Ensure we have exactly 12 bars
    const normalizedBars = Array.from({ length: 12 }, (_, i) => {
      const barValue = displayBars[i] || 0;
      return Math.max(0.15, Math.min(1, barValue));
    });

    return (
      <div className="flex items-end justify-center gap-2 h-20 px-6 py-4 bg-gray-50 rounded-lg border">
        {normalizedBars.map((height, index) => (
          <div
            key={index}
            className="rounded-full transition-all duration-150 ease-out"
            style={{
              height: `${height * 80}%`,
              width: "6px",
              minHeight: "12px",
              background: "linear-gradient(to top, #ae00d0, #7b5aff)",
              opacity: 0.8 + height * 0.2,
            }}
          />
        ))}
      </div>
    );
  } catch (error) {
    console.error("AudioWaveformVisualizer error:", error);
    return (
      <div className="flex items-end justify-center gap-2 h-20 px-6 py-4 bg-gray-50 rounded-lg border">
        {Array.from({ length: 12 }).map((_, index) => (
          <div
            key={index}
            className="rounded-full"
            style={{
              height: "24px",
              width: "6px",
              backgroundColor: "#ae00d0",
              opacity: 0.6,
            }}
          />
        ))}
      </div>
    );
  }
};

/**
 * Custom Agent Controls Component
 */
const CustomAgentControls = ({
  onDisconnect,
}: {
  onDisconnect: () => void;
}) => {
  return (
    <div className="flex flex-col h-full p-6 bg-white">
      {/* Audio Waveform Visualizer */}
      <div className="flex-1 flex flex-col gap-4 items-center justify-center min-h-0">
        <div className="w-full max-w-sm">
          <AudioWaveformVisualizer />
        </div>

        <div className="flex justify-center gap-6 py-4">
          <MicToggleButton />
          <DisconnectButton onDisconnect={onDisconnect} />
        </div>
      </div>

      {/* Control Buttons - positioned right below visualizer */}

      {/* Status Text */}
      <div className="text-center pb-4">
        <p className="text-sm text-gray-600 font-medium">Agent Call Active</p>
      </div>
    </div>
  );
};

/**
 * LiveKitCallPanel component
 *
 * Displays the LiveKit call interface (expects to be within a LiveKitRoom context)
 */
export const LiveKitCallPanel = ({
  handleHidePanel,
}: LiveKitCallPanelProps) => {
  return (
    <div className="flex flex-col w-full h-full border-l border-gray-300 bg-white">
      {/* Header */}
      <div className="flex justify-between items-center p-4 border-b border-gray-300 bg-gray-50 flex-shrink-0">
        <h3 className="text-sm font-medium text-gray-900">Agent Call</h3>
        <button
          onClick={handleHidePanel}
          aria-label="Hide call panel"
          className="p-1 rounded-sm hover:bg-gray-200 transition-colors duration-200"
        >
          <X className="h-4 w-4 text-gray-600" />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* Keep RoomAudioRenderer for actual audio playback - hidden */}
        <div className="hidden">
          <RoomAudioRenderer />
        </div>

        {/* Custom Controls - takes full remaining space */}
        <div className="flex-1 min-h-0">
          <CustomAgentControls onDisconnect={handleHidePanel} />
        </div>
      </div>
    </div>
  );
};
